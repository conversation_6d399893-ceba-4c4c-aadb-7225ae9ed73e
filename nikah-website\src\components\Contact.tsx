import React from 'react';
import { Phone, Mail, MapPin, MessageCircle, Users, Clock } from 'lucide-react';

const Contact: React.FC = () => {
  const contacts = [
    {
      title: 'Координатор мероприятия',
      name: '<PERSON><PERSON><PERSON><PERSON> Ибрагимова',
      phone: '+7 (999) 123-45-67',
      email: 'a<PERSON><EMAIL>',
      description: 'По всем вопросам организации',
      icon: <Users className="h-6 w-6" />,
    },
    {
      title: 'Семья жениха',
      name: 'Ах<PERSON><PERSON><PERSON> Алиев',
      phone: '+7 (999) 234-56-78',
      email: '<EMAIL>',
      description: 'Вопросы от стороны жениха',
      icon: <Phone className="h-6 w-6" />,
    },
    {
      title: 'Семья невесты',
      name: 'М<PERSON><PERSON><PERSON>ям Хасанова',
      phone: '+7 (999) 345-67-89',
      email: '<EMAIL>',
      description: 'Вопросы от стороны невесты',
      icon: <Phone className="h-6 w-6" />,
    },
  ];

  return (
    <section id="contact" className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-4">
            Контакты
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Свяжитесь с нами по любым вопросам, связанным с мероприятием
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Левая колонка - Контактная информация */}
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8">
              <h3 className="text-2xl font-serif font-semibold text-gray-800 mb-6">
                Место проведения
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Мечеть "Нур"</h4>
                    <p className="text-gray-600">
                      ул. Центральная, 123<br />
                      г. Москва, 101000
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Время работы</h4>
                    <p className="text-gray-600">
                      Ежедневно: 6:00 - 22:00<br />
                      Пятница: 6:00 - 23:00
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <Phone className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Администрация мечети</h4>
                    <p className="text-gray-600">+7 (495) 123-45-67</p>
                  </div>
                </div>
              </div>

              {/* Карта (заглушка) */}
              <div className="mt-6 bg-gray-200 rounded-lg h-48 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <MapPin className="h-12 w-12 mx-auto mb-2" />
                  <p>Интерактивная карта</p>
                  <p className="text-sm">будет добавлена</p>
                </div>
              </div>
            </div>

            {/* Важная информация */}
            <div className="bg-gradient-to-br from-gold-50 to-gold-100 rounded-2xl p-8">
              <h3 className="text-2xl font-serif font-semibold text-gray-800 mb-4">
                Важная информация
              </h3>
              <div className="space-y-3 text-gray-600">
                <p>• Просим подтвердить присутствие до 1 июля 2024</p>
                <p>• Парковка доступна на территории мечети</p>
                <p>• Детская комната будет организована</p>
                <p>• Халяльное питание гарантировано</p>
                <p>• Фотосъемка разрешена в определенных зонах</p>
              </div>
            </div>
          </div>

          {/* Правая колонка - Контактные лица */}
          <div className="space-y-6">
            <h3 className="text-2xl font-serif font-semibold text-gray-800 mb-6">
              Контактные лица
            </h3>

            {contacts.map((contact, index) => (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-100 p-3 rounded-lg text-primary-600">
                    {contact.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-800 mb-1">
                      {contact.title}
                    </h4>
                    <p className="text-lg font-medium text-primary-700 mb-2">
                      {contact.name}
                    </p>
                    <p className="text-gray-600 text-sm mb-3">
                      {contact.description}
                    </p>
                    
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-gray-600">
                        <Phone className="h-4 w-4" />
                        <a 
                          href={`tel:${contact.phone}`}
                          className="hover:text-primary-600 transition-colors"
                        >
                          {contact.phone}
                        </a>
                      </div>
                      <div className="flex items-center space-x-2 text-gray-600">
                        <Mail className="h-4 w-4" />
                        <a 
                          href={`mailto:${contact.email}`}
                          className="hover:text-primary-600 transition-colors"
                        >
                          {contact.email}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Дополнительные способы связи */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                <MessageCircle className="h-5 w-5 mr-2" />
                Другие способы связи
              </h4>
              <div className="space-y-3 text-gray-600">
                <p>
                  <strong>WhatsApp группа:</strong> 
                  <a href="#" className="text-primary-600 hover:underline ml-1">
                    Присоединиться к группе
                  </a>
                </p>
                <p>
                  <strong>Telegram канал:</strong> 
                  <a href="#" className="text-primary-600 hover:underline ml-1">
                    @nikah_amir_fatima
                  </a>
                </p>
                <p>
                  <strong>Экстренная связь:</strong> +7 (999) 000-00-00
                </p>
              </div>
            </div>

            {/* Подарки */}
            <div className="bg-gradient-to-br from-gold-50 to-gold-100 rounded-xl p-6">
              <h4 className="font-semibold text-gray-800 mb-3">
                О подарках
              </h4>
              <p className="text-gray-600 text-sm">
                Ваше присутствие - лучший подарок для нас. Если вы хотите сделать 
                денежный подарок, это можно будет сделать в день мероприятия. 
                Список желаемых подарков доступен по запросу у координатора.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
