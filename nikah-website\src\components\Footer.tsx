import React from 'react';
import { Heart, Calendar, MapPin, Phone, Mail } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container-custom">
        {/* Основная часть футера */}
        <div className="py-12">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Левая колонка - Информация о мероприятии */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Heart className="h-8 w-8 text-primary-400" />
                <span className="text-2xl font-serif font-bold">Никах</span>
              </div>
              <p className="text-gray-300 mb-4">
                Приглашаем вас разделить с нами радость этого благословенного дня
              </p>
              <div className="space-y-2 text-gray-300">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>15 июля 2024</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>Мечеть "Нур", Москва</span>
                </div>
              </div>
            </div>

            {/* Средняя колонка - Быстрые ссылки */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Быстрые ссылки</h3>
              <nav className="space-y-2">
                {[
                  { id: 'home', label: 'Главная' },
                  { id: 'details', label: 'Детали мероприятия' },
                  { id: 'rsvp', label: 'Подтверждение' },
                  { id: 'gallery', label: 'Галерея' },
                  { id: 'contact', label: 'Контакты' },
                ].map((item) => (
                  <button
                    key={item.id}
                    onClick={() => {
                      const element = document.getElementById(item.id);
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                      }
                    }}
                    className="block text-gray-300 hover:text-primary-400 transition-colors duration-200"
                  >
                    {item.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Правая колонка - Контакты */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Контакты</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-primary-400" />
                  <div>
                    <p className="text-gray-300">Координатор</p>
                    <a 
                      href="tel:+79991234567" 
                      className="text-white hover:text-primary-400 transition-colors"
                    >
                      +7 (999) 123-45-67
                    </a>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-primary-400" />
                  <div>
                    <p className="text-gray-300">Email</p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-white hover:text-primary-400 transition-colors"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              {/* Социальные сети (если нужны) */}
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-300 mb-3">
                  Следите за обновлениями
                </h4>
                <div className="flex space-x-3">
                  <a 
                    href="#" 
                    className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors"
                    aria-label="WhatsApp"
                  >
                    <MessageCircle className="h-5 w-5" />
                  </a>
                  <a 
                    href="#" 
                    className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors"
                    aria-label="Telegram"
                  >
                    <Send className="h-5 w-5" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Разделитель */}
        <div className="border-t border-gray-800"></div>

        {/* Нижняя часть футера */}
        <div className="py-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              <p>© {currentYear} Никах Амира и Фатимы. Все права защищены.</p>
            </div>
            
            <div className="flex items-center space-x-6">
              <button
                onClick={scrollToTop}
                className="text-gray-400 hover:text-primary-400 transition-colors text-sm"
              >
                Наверх ↑
              </button>
              <span className="text-gray-600">|</span>
              <p className="text-gray-400 text-sm">
                Создано с ❤️ для особенного дня
              </p>
            </div>
          </div>
        </div>

        {/* Исламская цитата */}
        <div className="border-t border-gray-800 py-6">
          <div className="text-center">
            <blockquote className="text-gray-300 italic text-sm max-w-2xl mx-auto">
              "Бисмилляхи ррахмани ррахим. Во имя Аллаха Милостивого, Милосердного"
            </blockquote>
          </div>
        </div>
      </div>
    </footer>
  );
};

// Импортируем недостающие иконки
import { MessageCircle, Send } from 'lucide-react';

export default Footer;
