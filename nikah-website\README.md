# Сайт Никаха - Современное веб-приложение для свадебного мероприятия

Современный, отзывчивый сайт для исламской свадебной церемонии (никах), созданный с использованием React, TypeScript, Vite и Tailwind CSS.

## 🌟 Особенности

- **Современный дизайн**: Элегантный и культурно-подходящий дизайн
- **Отзывчивая верстка**: Оптимизирован для мобильных устройств и десктопа
- **RSVP функциональность**: Форма подтверждения присутствия с валидацией
- **Фотогалерея**: Интерактивная галерея с модальными окнами
- **Плавная навигация**: Smooth scroll между секциями
- **Анимации**: Современные CSS анимации и переходы
- **Доступность**: Соответствие принципам веб-доступности

## 🛠 Технологический стек

- **Frontend**: React 18 + TypeScript
- **Сборщик**: Vite
- **Стилизация**: Tailwind CSS
- **Иконки**: Lucide React
- **Формы**: React Hook Form
- **Уведомления**: React Hot Toast
- **База данных**: Готов для интеграции с Supabase

## 📦 Установка и запуск

### Предварительные требования

- Node.js (версия 16 или выше)
- npm или yarn

### Установка

1. Клонируйте репозиторий или скачайте файлы проекта
2. Установите зависимости:

```bash
npm install
```

### Запуск в режиме разработки

```bash
npm run dev
```

Приложение будет доступно по адресу `http://localhost:5173`

### Сборка для продакшена

```bash
npm run build
```

Собранные файлы будут находиться в папке `dist/`

### Предварительный просмотр продакшен сборки

```bash
npm run preview
```

## 📁 Структура проекта

```
nikah-website/
├── public/                 # Статические файлы
├── src/
│   ├── components/         # React компоненты
│   │   ├── Header.tsx     # Навигация
│   │   ├── Hero.tsx       # Главная секция
│   │   ├── EventDetails.tsx # Детали мероприятия
│   │   ├── RSVP.tsx       # Форма подтверждения
│   │   ├── Gallery.tsx    # Фотогалерея
│   │   ├── Contact.tsx    # Контакты
│   │   └── Footer.tsx     # Подвал
│   ├── App.tsx            # Главный компонент
│   ├── main.tsx           # Точка входа
│   └── index.css          # Глобальные стили
├── tailwind.config.js     # Конфигурация Tailwind
├── postcss.config.js      # Конфигурация PostCSS
└── package.json           # Зависимости проекта
```

## 🎨 Кастомизация

### Изменение цветовой схемы

Отредактируйте файл `tailwind.config.js` для изменения цветов:

```js
colors: {
  primary: {
    // Ваши цвета
  },
  gold: {
    // Ваши цвета
  }
}
```

### Изменение контента

Отредактируйте соответствующие компоненты в папке `src/components/` для изменения:
- Имен молодоженов
- Даты и места проведения
- Контактной информации
- Фотографий в галерее

### Добавление фотографий

Замените URL изображений в компоненте `Gallery.tsx` на ваши фотографии.

## 🔧 Интеграция с базой данных

Для полной функциональности RSVP рекомендуется интегрировать Supabase:

1. Создайте проект в Supabase
2. Настройте таблицу для RSVP ответов
3. Добавьте переменные окружения
4. Обновите компонент RSVP для работы с API

## 📱 Тестирование

### Рекомендуемые тесты

1. **Функциональные тесты**:
   - Навигация между секциями
   - Отправка RSVP формы
   - Открытие/закрытие галереи

2. **Тесты отзывчивости**:
   - Мобильные устройства (320px+)
   - Планшеты (768px+)
   - Десктоп (1024px+)

3. **Тесты производительности**:
   - Время загрузки страницы
   - Оптимизация изображений

### Запуск тестов

```bash
# Установите дополнительные зависимости для тестирования
npm install -D @testing-library/react @testing-library/jest-dom vitest

# Запуск тестов
npm run test
```

## 🚀 Деплой

### Vercel (рекомендуется)

1. Подключите репозиторий к Vercel
2. Настройте переменные окружения
3. Деплой произойдет автоматически

### Netlify

1. Подключите репозиторий к Netlify
2. Команда сборки: `npm run build`
3. Папка публикации: `dist`

### Другие платформы

Проект совместим с любыми статическими хостингами.

## 🤝 Поддержка

Если у вас возникли вопросы или проблемы:

1. Проверьте документацию
2. Создайте issue в репозитории
3. Свяжитесь с разработчиком

## 📄 Лицензия

Этот проект создан для личного использования. При использовании кода просьба указать авторство.

---

**Создано с ❤️ для особенного дня**
