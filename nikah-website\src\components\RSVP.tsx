import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Send, Check, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

interface RSVPFormData {
  name: string;
  email: string;
  phone: string;
  attendance: 'yes' | 'no' | 'maybe';
  guestCount: number;
  dietaryRestrictions: string;
  message: string;
}

const RSVP: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<RSVPFormData>();

  const attendance = watch('attendance');

  const onSubmit = async (data: RSVPFormData) => {
    setIsSubmitting(true);
    
    try {
      // Здесь будет интеграция с Supabase
      // Пока что симулируем отправку
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('RSVP Data:', data);
      
      toast.success('Спасибо! Ваш ответ получен.');
      setIsSubmitted(true);
      reset();
    } catch (error) {
      toast.error('Произошла ошибка. Попробуйте еще раз.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <section id="rsvp" className="section-padding bg-gradient-to-br from-primary-50 to-primary-100">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-2xl p-12 shadow-lg">
              <div className="mb-6">
                <Check className="h-16 w-16 text-green-500 mx-auto" />
              </div>
              <h2 className="text-3xl font-serif font-bold text-gray-800 mb-4">
                Спасибо за ваш ответ!
              </h2>
              <p className="text-gray-600 text-lg mb-8">
                Мы получили ваше подтверждение и свяжемся с вами при необходимости.
              </p>
              <button
                onClick={() => setIsSubmitted(false)}
                className="btn-secondary"
              >
                Отправить еще один ответ
              </button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="rsvp" className="section-padding bg-gradient-to-br from-primary-50 to-primary-100">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-4">
            Подтверждение присутствия
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Пожалуйста, подтвердите ваше присутствие до 1 июля 2024 года
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSubmit(onSubmit)} className="bg-white rounded-2xl p-8 shadow-lg">
            <div className="space-y-6">
              {/* Имя */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Полное имя *
                </label>
                <input
                  type="text"
                  {...register('name', { required: 'Имя обязательно' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Введите ваше полное имя"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.name.message}
                  </p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  {...register('email', { 
                    required: 'Email обязателен',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Неверный формат email'
                    }
                  })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Телефон */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Телефон
                </label>
                <input
                  type="tel"
                  {...register('phone')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="+7 (999) 123-45-67"
                />
              </div>

              {/* Присутствие */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Сможете ли вы присутствовать? *
                </label>
                <div className="space-y-3">
                  {[
                    { value: 'yes', label: 'Да, буду присутствовать', color: 'green' },
                    { value: 'no', label: 'К сожалению, не смогу', color: 'red' },
                    { value: 'maybe', label: 'Пока не уверен(а)', color: 'yellow' },
                  ].map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        value={option.value}
                        {...register('attendance', { required: 'Выберите вариант' })}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                      />
                      <span className="ml-3 text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
                {errors.attendance && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.attendance.message}
                  </p>
                )}
              </div>

              {/* Количество гостей (только если присутствие подтверждено) */}
              {attendance === 'yes' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Количество гостей (включая вас)
                  </label>
                  <select
                    {...register('guestCount', { valueAsNumber: true })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    {[1, 2, 3, 4, 5].map((num) => (
                      <option key={num} value={num}>
                        {num} {num === 1 ? 'человек' : 'человека'}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Диетические ограничения */}
              {attendance === 'yes' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Диетические ограничения или аллергии
                  </label>
                  <textarea
                    {...register('dietaryRestrictions')}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Укажите любые диетические ограничения или аллергии..."
                  />
                </div>
              )}

              {/* Сообщение */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Пожелания молодоженам
                </label>
                <textarea
                  {...register('message')}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Поделитесь своими пожеланиями и поздравлениями..."
                />
              </div>

              {/* Кнопка отправки */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full btn-primary flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Отправка...</span>
                  </>
                ) : (
                  <>
                    <Send className="h-5 w-5" />
                    <span>Отправить ответ</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default RSVP;
