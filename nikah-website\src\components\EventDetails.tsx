import React from 'react';
import { Calendar, Clock, MapPin, Users, Car, Gift } from 'lucide-react';

const EventDetails: React.FC = () => {
  const events = [
    {
      title: 'Никах (Церемония бракосочетания)',
      time: '14:00',
      description: 'Торжественная религиозная церемония бракосочетания',
      icon: <Calendar className="h-6 w-6" />,
    },
    {
      title: 'Праздничный обед',
      time: '15:30',
      description: 'Совместная трапеза с традиционными блюдами',
      icon: <Users className="h-6 w-6" />,
    },
    {
      title: 'Торжественная часть',
      time: '17:00',
      description: 'Поздравления, выступления и праздничная программа',
      icon: <Gift className="h-6 w-6" />,
    },
  ];

  return (
    <section id="details" className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-800 mb-4">
            Детали мероприятия
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Присоединяйтесь к нам в этот особенный день для празднования союза двух сердец
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Левая колонка - Основная информация */}
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8">
              <h3 className="text-2xl font-serif font-semibold text-gray-800 mb-6">
                Основная информация
              </h3>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Дата</h4>
                    <p className="text-gray-600">Суббота, 15 июля 2024</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Время</h4>
                    <p className="text-gray-600">Начало в 14:00</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Место проведения</h4>
                    <p className="text-gray-600">
                      Мечеть "Нур"<br />
                      ул. Центральная, 123<br />
                      г. Москва
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-primary-600 p-3 rounded-lg">
                    <Car className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Парковка</h4>
                    <p className="text-gray-600">
                      Бесплатная парковка доступна на территории
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Дресс-код */}
            <div className="bg-gradient-to-br from-gold-50 to-gold-100 rounded-2xl p-8">
              <h3 className="text-2xl font-serif font-semibold text-gray-800 mb-4">
                Дресс-код
              </h3>
              <p className="text-gray-600 mb-4">
                Просим соблюдать скромный стиль одежды в соответствии с исламскими традициями:
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• Для женщин: закрытая одежда, платок</li>
                <li>• Для мужчин: деловой или национальный костюм</li>
                <li>• Предпочтительные цвета: светлые, пастельные тона</li>
              </ul>
            </div>
          </div>

          {/* Правая колонка - Программа мероприятия */}
          <div>
            <h3 className="text-2xl font-serif font-semibold text-gray-800 mb-8">
              Программа дня
            </h3>
            
            <div className="space-y-6">
              {events.map((event, index) => (
                <div
                  key={index}
                  className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary-100 p-3 rounded-lg text-primary-600">
                      {event.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-800">
                          {event.title}
                        </h4>
                        <span className="text-primary-600 font-medium">
                          {event.time}
                        </span>
                      </div>
                      <p className="text-gray-600">{event.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Дополнительная информация */}
            <div className="mt-8 p-6 bg-gray-50 rounded-xl">
              <h4 className="font-semibold text-gray-800 mb-3">
                Важная информация
              </h4>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Мероприятие будет проводиться на русском и арабском языках</li>
                <li>• Фотосъемка разрешена в определенных зонах</li>
                <li>• Детская зона будет организована</li>
                <li>• Халяльное питание гарантировано</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EventDetails;
