import React from 'react';
import { Heart, Calendar, MapPin } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToRSVP = () => {
    const element = document.getElementById('rsvp');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 via-white to-gold-50"
    >
      {/* Фоновые декоративные элементы */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary-100 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-gold-100 rounded-full opacity-30 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-primary-200 rounded-full opacity-25 animate-pulse delay-500"></div>
      </div>

      <div className="container-custom text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Иконка сердца */}
          <div className="mb-8 animate-fade-in">
            <Heart className="h-16 w-16 text-primary-600 mx-auto mb-4" />
          </div>

          {/* Заголовок */}
          <h1 className="text-5xl md:text-7xl font-serif font-bold text-gray-800 mb-6 animate-slide-up">
            Никах
          </h1>

          {/* Имена */}
          <div className="mb-8 animate-slide-up delay-200">
            <p className="text-2xl md:text-3xl text-gray-600 mb-2">
              Приглашаем вас на торжественную церемонию
            </p>
            <h2 className="text-4xl md:text-5xl font-serif font-semibold text-primary-700">
              Амир & Фатима
            </h2>
          </div>

          {/* Краткая информация */}
          <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8 mb-12 animate-slide-up delay-300">
            <div className="flex items-center space-x-2 text-gray-600">
              <Calendar className="h-5 w-5" />
              <span className="text-lg">15 июля 2024</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-600">
              <MapPin className="h-5 w-5" />
              <span className="text-lg">Мечеть "Нур"</span>
            </div>
          </div>

          {/* Кнопки действий */}
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 animate-slide-up delay-500">
            <button
              onClick={scrollToRSVP}
              className="btn-primary text-lg px-8 py-4"
            >
              Подтвердить присутствие
            </button>
            <button
              onClick={() => document.getElementById('details')?.scrollIntoView({ behavior: 'smooth' })}
              className="btn-secondary text-lg px-8 py-4"
            >
              Узнать подробности
            </button>
          </div>

          {/* Цитата */}
          <div className="mt-16 animate-slide-up delay-700">
            <blockquote className="text-xl md:text-2xl text-gray-600 italic font-light max-w-3xl mx-auto">
              "И среди Его знамений то, что Он сотворил для вас из вас самих жён, 
              чтобы вы находили в них успокоение, и установил между вами любовь и милосердие."
            </blockquote>
            <cite className="block mt-4 text-gray-500 text-lg">
              — Коран, 30:21
            </cite>
          </div>
        </div>
      </div>

      {/* Стрелка вниз */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
